'use client';

import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { setSidebarOpen, toggleSidebar } from '@/store/slices/uiSlice';
import HomeSidebar from '@/components/home/<USER>';
import HomeHeader from '@/components/home/<USER>';
import MobileFooterNav from '@/components/MobileFooterNav';

interface CustomerLayoutProps {
  children: React.ReactNode;
}

export default function CustomerLayout({ children }: CustomerLayoutProps) {
  // Use Redux for persistent sidebar state across remounts
  const dispatch = useDispatch();
  const sidebarOpen = useSelector((state: any) => state.ui.sidebarOpen);

  // Use local state for collapse since it's visual-only and should reset on navigation
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);

  // Handle expand sidebar and navigate to specific page
  const handleExpandAndNavigate = (href: string, categoryName: string) => {
    // First expand the sidebar
    setSidebarCollapsed(false);

    // Close mobile sidebar if open
    dispatch(setSidebarOpen(false));

    // Optional: Add a small delay to show the expansion animation
    setTimeout(() => {
      console.log(`Expanded sidebar and navigated to ${href} from ${categoryName} category`);
    }, 300);
  };

  const handleToggleCollapse = () => {
    setSidebarCollapsed(!sidebarCollapsed);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header - Fixed at top, starts after sidebar on desktop */}
      <HomeHeader
        onMenuClick={() => dispatch(setSidebarOpen(true))}
        sidebarCollapsed={sidebarCollapsed}
      />

      {/* Sidebar - Fixed on left, starts below header */}
      <HomeSidebar
        isOpen={sidebarOpen}
        onClose={() => dispatch(setSidebarOpen(false))}
        isCollapsed={sidebarCollapsed}
        onToggleCollapse={handleToggleCollapse}
        onExpandAndNavigate={handleExpandAndNavigate}
      />

      {/* Main Content - Positioned after header height and sidebar width */}
      <main className={`transition-all duration-300 ${
        sidebarCollapsed ? 'lg:ml-16' : 'lg:ml-64'
      } pt-14 lg:pt-16`}>
        <div className="px-3 pt-8 pb-5 lg:p-6">
          {children}
        </div>
      </main>

      <MobileFooterNav />
    </div>
  );
}
